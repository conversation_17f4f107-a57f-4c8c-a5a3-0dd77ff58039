import yaml
import requests
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Load YAML file
with open('issues.yaml', 'r') as file:
    issues_data = yaml.safe_load(file)

# Get Linear API key from environment variables
api_key = os.getenv('LINEAR_API_KEY')

# Check if API key is available
if not api_key:
    raise ValueError("LINEAR_API_KEY not found in .env file. Please make sure the .env file exists and contains the LINEAR_API_KEY variable.")

# Linear API endpoint for creating issues
url = 'https://api.linear.app/graphql'

# Headers for authentication
headers = {
    'Authorization': f'{api_key}',
    'Content-Type': 'application/json',
}

# Function to create an issue in Linear
def create_issue(issue_data):
    # GraphQL mutation for creating an issue
    mutation = '''
    mutation($title: String!, $description: String, $teamId: String!, $projectId: String!) {
      issueCreate(input: {title: $title, description: $description, teamId: $teamId, projectId: $projectId}) {
        success
        issue {
          id
          title
        }
      }
    }
    '''
    
    # Data to be sent with the request
    data = {
        'query': mutation,
        'variables': {
            'title': issue_data['title'],
            'description': issue_data.get('description', ''),
            'teamId': issue_data['teamId'],
            'projectId': issue_data['projectId']
        }
    }
    
    # Sending the request
    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code == 200:
        print(f"Issue created successfully: {response.json()}")
    else:
        print(f"Failed to create issue: {response.text}")

# Loop through the issues in the YAML file and create them
for issue in issues_data['issues']:
    create_issue(issue)
