# Linear Issue Creator

This Node.js script creates issues in Linear from a YAML file.

## Prerequisites

- Node.js installed
- A Linear API key

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file in the root directory with your Linear API key:
   ```
   LINEAR_API_KEY=your_api_key_here
   ```

   You can copy the provided `.env.example` file as a starting point:
   ```
   cp .env.example .env
   ```
   Then edit the `.env` file with your actual API key.

   **Note:** The `.env` file containing your actual API key should not be committed to your Git repository. Consider adding it to your `.gitignore` file.

3. Modify the `issues.yaml` file to include the issues you want to create.

## Usage

Run the script with:
```
npm run create
```

## YAML Structure

The `issues.yaml` file should have the following structure:

```yaml
issues:
  - title: "[TASK] Example Task"
    description: |
      # Background
      Task background information

      # Acceptance Criteria
      - Criteria 1
      - Criteria 2

    teamId: "your_team_id"
    projectId: "your_project_id"
```
